
    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .title { font: bold 24px sans-serif; fill: #2c3e50; }
          .subtitle { font: bold 18px sans-serif; fill: #34495e; }
          .text { font: 14px sans-serif; fill: #2c3e50; }
          .box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
          .arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
          .buy-box { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; }
          .sell-box { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
        </marker>
      </defs>
      
      <!-- Title -->
      <text x="400" y="30" text-anchor="middle" class="title">Trading Basics: Buy Low, Sell High</text>
      
      <!-- Price Chart Background -->
      <rect x="50" y="80" width="700" height="300" class="box" />
      <text x="400" y="105" text-anchor="middle" class="subtitle">Price Movement Over Time</text>
      
      <!-- Price Line -->
      <polyline points="80,300 150,250 220,280 290,200 360,230 430,180 500,220 570,160 640,190 720,140" 
                stroke="#3498db" stroke-width="3" fill="none" />
      
      <!-- Buy Points -->
      <circle cx="150" cy="250" r="8" fill="#27ae60" />
      <text x="150" y="275" text-anchor="middle" class="text">BUY</text>
      <text x="150" y="290" text-anchor="middle" class="text">$100</text>
      
      <circle cx="360" cy="230" r="8" fill="#27ae60" />
      <text x="360" y="255" text-anchor="middle" class="text">BUY</text>
      <text x="360" y="270" text-anchor="middle" class="text">$110</text>
      
      <!-- Sell Points -->
      <circle cx="290" cy="200" r="8" fill="#e74c3c" />
      <text x="290" y="185" text-anchor="middle" class="text">SELL</text>
      <text x="290" y="170" text-anchor="middle" class="text">$120</text>
      
      <circle cx="570" cy="160" r="8" fill="#e74c3c" />
      <text x="570" y="145" text-anchor="middle" class="text">SELL</text>
      <text x="570" y="130" text-anchor="middle" class="text">$140</text>
      
      <!-- Profit Arrows -->
      <path d="M 150 240 Q 220 180 290 190" class="arrow" />
      <text x="220" y="200" text-anchor="middle" class="text" fill="#27ae60">+$20 Profit</text>
      
      <path d="M 360 220 Q 465 140 570 150" class="arrow" />
      <text x="465" y="160" text-anchor="middle" class="text" fill="#27ae60">+$30 Profit</text>
      
      <!-- Key Concepts -->
      <rect x="50" y="420" width="200" height="120" class="buy-box" />
      <text x="150" y="445" text-anchor="middle" class="subtitle">BUYING (Going Long)</text>
      <text x="60" y="470" class="text">• Buy when price is low</text>
      <text x="60" y="490" class="text">• Expect price to rise</text>
      <text x="60" y="510" class="text">• Profit = Sell Price - Buy Price</text>
      <text x="60" y="530" class="text">• Example: Buy $100, Sell $120 = $20 profit</text>
      
      <rect x="300" y="420" width="200" height="120" class="sell-box" />
      <text x="400" y="445" text-anchor="middle" class="subtitle">SELLING (Going Short)</text>
      <text x="310" y="470" class="text">• Sell when price is high</text>
      <text x="310" y="490" class="text">• Expect price to fall</text>
      <text x="310" y="510" class="text">• Profit = Sell Price - Buy Price</text>
      <text x="310" y="530" class="text">• Example: Sell $120, Buy $100 = $20 profit</text>
      
      <rect x="550" y="420" width="200" height="120" class="box" />
      <text x="650" y="445" text-anchor="middle" class="subtitle">Key Rules</text>
      <text x="560" y="470" class="text">• Always use stop losses</text>
      <text x="560" y="490" class="text">• Risk only 1-2% per trade</text>
      <text x="560" y="510" class="text">• Plan your trades</text>
      <text x="560" y="530" class="text">• Control your emotions</text>
    </svg>