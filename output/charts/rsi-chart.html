
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
        <canvas id="rsiChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('rsiChart').getContext('2d');
            
            // Sample RSI data
            const rsiData = [45, 52, 48, 65, 72, 68, 75, 82, 78, 71, 65, 58, 42, 35, 28, 32, 38, 45, 52, 58];
            const labels = Array.from({length: 20}, (_, i) => `Day ${i + 1}`);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'RSI',
                        data: rsiData,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'RSI (Relative Strength Index) - Overbought/Oversold Indicator',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        y: {
                            min: 0,
                            max: 100,
                            title: {
                                display: true,
                                text: 'RSI Value'
                            }
                        }
                    },
                    elements: {
                        point: {
                            backgroundColor: function(context) {
                                const value = context.parsed.y;
                                if (value > 70) return 'red';
                                if (value < 30) return 'green';
                                return 'blue';
                            }
                        }
                    },
                    plugins: {
                        annotation: {
                            annotations: {
                                overbought: {
                                    type: 'line',
                                    yMin: 70,
                                    yMax: 70,
                                    borderColor: 'red',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    label: {
                                        content: 'Overbought (70)',
                                        enabled: true
                                    }
                                },
                                oversold: {
                                    type: 'line',
                                    yMin: 30,
                                    yMax: 30,
                                    borderColor: 'green',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    label: {
                                        content: 'Oversold (30)',
                                        enabled: true
                                    }
                                }
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>