
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    </head>
    <body>
        <canvas id="movingAverageChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('movingAverageChart').getContext('2d');
            
            // Sample price data with moving averages
            const data = [
                {date: '2024-01-01', price: 1.1850, sma20: 1.1840, sma50: 1.1820},
                {date: '2024-01-02', price: 1.1920, sma20: 1.1850, sma50: 1.1825},
                {date: '2024-01-03', price: 1.1900, sma20: 1.1860, sma50: 1.1830},
                {date: '2024-01-04', price: 1.1940, sma20: 1.1870, sma50: 1.1835},
                {date: '2024-01-05', price: 1.1970, sma20: 1.1885, sma50: 1.1840},
                {date: '2024-01-08', price: 1.1950, sma20: 1.1895, sma50: 1.1845},
                {date: '2024-01-09', price: 1.1930, sma20: 1.1900, sma50: 1.1850},
                {date: '2024-01-10', price: 1.1920, sma20: 1.1905, sma50: 1.1855}
            ];

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(d => d.date),
                    datasets: [{
                        label: 'Price',
                        data: data.map(d => d.price),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'SMA 20',
                        data: data.map(d => d.sma20),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1
                    }, {
                        label: 'SMA 50',
                        data: data.map(d => d.sma50),
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Moving Average Crossover Strategy',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>