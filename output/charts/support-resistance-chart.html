
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    </head>
    <body>
        <canvas id="supportResistanceChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('supportResistanceChart').getContext('2d');
            
            // Price data
            const priceData = [
                {x: '2024-01-01', y: 1.1850},
                {x: '2024-01-02', y: 1.1920},
                {x: '2024-01-03', y: 1.1900},
                {x: '2024-01-04', y: 1.1940},
                {x: '2024-01-05', y: 1.1970},
                {x: '2024-01-08', y: 1.1950},
                {x: '2024-01-09', y: 1.1930},
                {x: '2024-01-10', y: 1.1920},
                {x: '2024-01-11', y: 1.1960},
                {x: '2024-01-12', y: 1.1980}
            ];

            // Support and Resistance levels
            const supportLevel = 1.1850;
            const resistanceLevel = 1.1980;

            new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'EUR/USD Price',
                        data: priceData,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'Support Level',
                        data: priceData.map(point => ({x: point.x, y: supportLevel})),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        borderDash: [5, 5],
                        pointRadius: 0
                    }, {
                        label: 'Resistance Level',
                        data: priceData.map(point => ({x: point.x, y: resistanceLevel})),
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        borderDash: [5, 5],
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Support and Resistance Levels',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>