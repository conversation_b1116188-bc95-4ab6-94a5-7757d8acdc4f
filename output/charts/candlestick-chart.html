
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>
    </head>
    <body>
        <canvas id="candlestickChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('candlestickChart').getContext('2d');
            
            // Sample candlestick data
            const data = [
                {x: '2024-01-01', o: 1.1850, h: 1.1920, l: 1.1830, c: 1.1890},
                {x: '2024-01-02', o: 1.1890, h: 1.1950, l: 1.1870, c: 1.1920},
                {x: '2024-01-03', o: 1.1920, h: 1.1940, l: 1.1880, c: 1.1900},
                {x: '2024-01-04', o: 1.1900, h: 1.1960, l: 1.1890, c: 1.1940},
                {x: '2024-01-05', o: 1.1940, h: 1.1980, l: 1.1920, c: 1.1970},
                {x: '2024-01-08', o: 1.1970, h: 1.1990, l: 1.1930, c: 1.1950},
                {x: '2024-01-09', o: 1.1950, h: 1.1970, l: 1.1910, c: 1.1930},
                {x: '2024-01-10', o: 1.1930, h: 1.1950, l: 1.1900, c: 1.1920}
            ];

            new Chart(ctx, {
                type: 'candlestick',
                data: {
                    datasets: [{
                        label: 'EUR/USD',
                        data: data,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Candlestick Chart Example - EUR/USD',
                            font: { size: 16 }
                        },
                        legend: {
                            display: true
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            },
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>