import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Utility functions for the trading education analysis system
 */

export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function sanitizeText(text: string): string {
  return text
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s\-.,!?()]/g, '')
    .substring(0, 500); // Limit length
}

export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-z0-9]/gi, '_')
    .toLowerCase()
    .substring(0, 100);
}

export async function ensureDirectoryExists(dirPath: string): Promise<void> {
  await fs.ensureDir(dirPath);
}

export async function saveJsonData(data: any, filePath: string): Promise<void> {
  await ensureDirectoryExists(path.dirname(filePath));
  await fs.writeJson(filePath, data, { spaces: 2 });
}

export async function loadJsonData<T>(filePath: string): Promise<T | null> {
  try {
    if (await fs.pathExists(filePath)) {
      return await fs.readJson(filePath);
    }
    return null;
  } catch (error) {
    console.error(`Error loading JSON from ${filePath}:`, error);
    return null;
  }
}

export function extractKeywords(text: string): string[] {
  const keywords: string[] = [];
  const lowerText = text.toLowerCase();
  
  // Trading-specific keywords
  const tradingKeywords = [
    // Market types
    'forex', 'stocks', 'cryptocurrency', 'crypto', 'bitcoin', 'ethereum',
    'options', 'futures', 'commodities', 'bonds', 'etf', 'mutual funds',
    
    // Trading styles
    'day trading', 'swing trading', 'scalping', 'position trading',
    'algorithmic trading', 'high frequency trading',
    
    // Analysis types
    'technical analysis', 'fundamental analysis', 'sentiment analysis',
    'quantitative analysis', 'chart analysis',
    
    // Indicators and tools
    'moving average', 'rsi', 'macd', 'bollinger bands', 'fibonacci',
    'support', 'resistance', 'trend lines', 'candlestick patterns',
    'volume', 'momentum', 'oscillators',
    
    // Risk management
    'risk management', 'stop loss', 'take profit', 'position sizing',
    'portfolio management', 'diversification', 'hedging',
    
    // Psychology and discipline
    'trading psychology', 'discipline', 'emotions', 'fear', 'greed',
    'patience', 'mindset', 'mental health',
    
    // Strategies
    'breakout', 'reversal', 'trend following', 'mean reversion',
    'arbitrage', 'pairs trading', 'grid trading',
    
    // Market concepts
    'bull market', 'bear market', 'volatility', 'liquidity',
    'market makers', 'spread', 'slippage', 'gap',
    
    // Education and learning
    'beginner', 'intermediate', 'advanced', 'course', 'tutorial',
    'strategy', 'tips', 'guide', 'education', 'learning'
  ];

  tradingKeywords.forEach(keyword => {
    if (lowerText.includes(keyword)) {
      keywords.push(keyword);
    }
  });

  return [...new Set(keywords)]; // Remove duplicates
}

export function categorizeContent(text: string): string {
  const lowerText = text.toLowerCase();
  
  if (lowerText.includes('beginner') || lowerText.includes('basic') || 
      lowerText.includes('introduction') || lowerText.includes('start')) {
    return 'beginner';
  }
  
  if (lowerText.includes('advanced') || lowerText.includes('expert') || 
      lowerText.includes('professional') || lowerText.includes('master')) {
    return 'advanced';
  }
  
  if (lowerText.includes('strategy') || lowerText.includes('technique')) {
    return 'strategy';
  }
  
  if (lowerText.includes('psychology') || lowerText.includes('mindset') || 
      lowerText.includes('emotion')) {
    return 'psychology';
  }
  
  if (lowerText.includes('risk') || lowerText.includes('management')) {
    return 'risk_management';
  }
  
  if (lowerText.includes('technical') || lowerText.includes('chart') || 
      lowerText.includes('indicator')) {
    return 'technical_analysis';
  }
  
  if (lowerText.includes('fundamental') || lowerText.includes('economic') || 
      lowerText.includes('news')) {
    return 'fundamental_analysis';
  }
  
  return 'general';
}

export function formatDuration(duration: string): string {
  // Convert various duration formats to a standard format
  const durationLower = duration.toLowerCase().trim();
  
  // Handle YouTube duration format (e.g., "1:23:45")
  const timeMatch = durationLower.match(/(\d+):(\d+)(?::(\d+))?/);
  if (timeMatch) {
    const hours = parseInt(timeMatch[1]);
    const minutes = parseInt(timeMatch[2]);
    const seconds = timeMatch[3] ? parseInt(timeMatch[3]) : 0;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m ${seconds}s`;
    }
  }
  
  // Handle text duration formats
  if (durationLower.includes('hour')) {
    const hourMatch = durationLower.match(/(\d+)\s*hour/);
    if (hourMatch) {
      return `${hourMatch[1]}h`;
    }
  }
  
  if (durationLower.includes('minute')) {
    const minuteMatch = durationLower.match(/(\d+)\s*minute/);
    if (minuteMatch) {
      return `${minuteMatch[1]}m`;
    }
  }
  
  return duration;
}

export function generateId(prefix: string = ''): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}${prefix ? '_' : ''}${timestamp}_${random}`;
}

export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

export function calculateProgress(current: number, total: number): number {
  return Math.round((current / total) * 100);
}

export function logProgress(message: string, current: number, total: number): void {
  const progress = calculateProgress(current, total);
  console.log(`[${progress}%] ${message} (${current}/${total})`);
}
