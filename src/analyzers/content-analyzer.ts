import { TradingCourse, TradingConcept, AnalysisResult, LearningPath, LearningModule, TradingStrategy } from '../types';
import { extractKeywords, categorizeContent, generateId } from '../utils/helpers';

export class ContentAnalyzer {
  
  analyzeScrapedData(courses: TradingCourse[], concepts: TradingConcept[]): AnalysisResult {
    console.log('🔍 Analyzing scraped trading education data...');

    const analysis: AnalysisResult = {
      totalCourses: courses.length,
      conceptsExtracted: concepts.length,
      topicDistribution: this.analyzeTopicDistribution(courses),
      difficultyDistribution: this.analyzeDifficultyDistribution(courses),
      platformDistribution: this.analyzePlatformDistribution(courses),
      averageRating: this.calculateAverageRating(courses),
      recommendations: this.generateRecommendations(courses, concepts),
      generatedAt: new Date()
    };

    return analysis;
  }

  private analyzeTopicDistribution(courses: TradingCourse[]): Record<string, number> {
    const topicCounts: Record<string, number> = {};

    courses.forEach(course => {
      course.topics.forEach(topic => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });
    });

    // Sort by frequency and return top topics
    const sortedTopics = Object.entries(topicCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
      .reduce((obj, [topic, count]) => {
        obj[topic] = count;
        return obj;
      }, {} as Record<string, number>);

    return sortedTopics;
  }

  private analyzeDifficultyDistribution(courses: TradingCourse[]): Record<string, number> {
    const difficultyCount: Record<string, number> = {
      beginner: 0,
      intermediate: 0,
      advanced: 0
    };

    courses.forEach(course => {
      difficultyCount[course.level]++;
    });

    return difficultyCount;
  }

  private analyzePlatformDistribution(courses: TradingCourse[]): Record<string, number> {
    const platformCount: Record<string, number> = {};

    courses.forEach(course => {
      platformCount[course.platform] = (platformCount[course.platform] || 0) + 1;
    });

    return platformCount;
  }

  private calculateAverageRating(courses: TradingCourse[]): number {
    const coursesWithRating = courses.filter(course => course.rating && course.rating > 0);
    
    if (coursesWithRating.length === 0) return 0;

    const totalRating = coursesWithRating.reduce((sum, course) => sum + (course.rating || 0), 0);
    return Math.round((totalRating / coursesWithRating.length) * 100) / 100;
  }

  private generateRecommendations(courses: TradingCourse[], concepts: TradingConcept[]): string[] {
    const recommendations: string[] = [];

    // Analyze course quality and content gaps
    const beginnerCourses = courses.filter(c => c.level === 'beginner');
    const advancedCourses = courses.filter(c => c.level === 'advanced');

    if (beginnerCourses.length < courses.length * 0.3) {
      recommendations.push('More beginner-friendly content is needed to help newcomers start their trading journey');
    }

    if (advancedCourses.length < courses.length * 0.2) {
      recommendations.push('Additional advanced trading strategies and techniques should be covered');
    }

    // Topic-based recommendations
    const topicCounts = this.analyzeTopicDistribution(courses);
    const topTopics = Object.keys(topicCounts).slice(0, 5);

    if (!topTopics.includes('risk management')) {
      recommendations.push('Risk management is crucial and should be emphasized more in trading education');
    }

    if (!topTopics.includes('trading psychology')) {
      recommendations.push('Trading psychology and emotional control are essential topics that need more coverage');
    }

    // Platform diversity recommendations
    const platforms = Object.keys(this.analyzePlatformDistribution(courses));
    if (platforms.length < 3) {
      recommendations.push('Diversifying content sources across more platforms would provide broader perspectives');
    }

    // Concept coverage recommendations
    const conceptCategories = concepts.map(c => c.category);
    const uniqueCategories = [...new Set(conceptCategories)];

    if (uniqueCategories.length < 5) {
      recommendations.push('Expanding concept coverage across more trading categories would enhance learning');
    }

    return recommendations;
  }

  generateLearningPaths(courses: TradingCourse[], concepts: TradingConcept[]): LearningPath[] {
    console.log('📚 Generating structured learning paths...');

    const paths: LearningPath[] = [];

    // Beginner Learning Path
    const beginnerPath = this.createBeginnerPath(courses, concepts);
    paths.push(beginnerPath);

    // Intermediate Learning Path
    const intermediatePath = this.createIntermediatePath(courses, concepts);
    paths.push(intermediatePath);

    // Advanced Learning Path
    const advancedPath = this.createAdvancedPath(courses, concepts);
    paths.push(advancedPath);

    // Specialized paths
    const forexPath = this.createSpecializedPath('Forex Trading', courses, concepts, 'forex');
    paths.push(forexPath);

    const stockPath = this.createSpecializedPath('Stock Trading', courses, concepts, 'stocks');
    paths.push(stockPath);

    const cryptoPath = this.createSpecializedPath('Cryptocurrency Trading', courses, concepts, 'crypto');
    paths.push(cryptoPath);

    return paths;
  }

  private createBeginnerPath(courses: TradingCourse[], concepts: TradingConcept[]): LearningPath {
    const beginnerCourses = courses.filter(c => c.level === 'beginner');
    const beginnerConcepts = concepts.filter(c => c.difficulty === 'beginner');

    const modules: LearningModule[] = [
      {
        id: generateId('module'),
        title: 'Trading Fundamentals',
        description: 'Basic concepts and terminology in trading',
        concepts: beginnerConcepts.slice(0, 10).map(c => c.name),
        practicalExercises: [
          'Set up a demo trading account',
          'Practice reading basic charts',
          'Identify major currency pairs'
        ],
        resources: beginnerCourses.slice(0, 5).map(c => c.url),
        estimatedTime: '2 weeks'
      },
      {
        id: generateId('module'),
        title: 'Market Analysis Basics',
        description: 'Introduction to technical and fundamental analysis',
        concepts: ['technical analysis', 'fundamental analysis', 'chart patterns'],
        practicalExercises: [
          'Analyze simple chart patterns',
          'Read economic news and its impact',
          'Practice with basic indicators'
        ],
        resources: beginnerCourses.filter(c => c.topics.includes('technical analysis')).map(c => c.url),
        estimatedTime: '3 weeks'
      },
      {
        id: generateId('module'),
        title: 'Risk Management',
        description: 'Essential risk management principles',
        concepts: ['risk management', 'position sizing', 'stop loss'],
        practicalExercises: [
          'Calculate position sizes',
          'Set stop loss levels',
          'Practice risk-reward ratios'
        ],
        resources: beginnerCourses.filter(c => c.topics.includes('risk management')).map(c => c.url),
        estimatedTime: '2 weeks'
      }
    ];

    return {
      id: generateId('path'),
      name: 'Beginner Trading Mastery',
      description: 'Complete foundation for new traders',
      level: 'beginner',
      estimatedDuration: '7-8 weeks',
      modules
    };
  }

  private createIntermediatePath(courses: TradingCourse[], concepts: TradingConcept[]): LearningPath {
    const intermediateCourses = courses.filter(c => c.level === 'intermediate');
    const intermediateConcepts = concepts.filter(c => c.difficulty === 'intermediate');

    const modules: LearningModule[] = [
      {
        id: generateId('module'),
        title: 'Advanced Technical Analysis',
        description: 'Complex chart patterns and indicators',
        concepts: intermediateConcepts.slice(0, 8).map(c => c.name),
        practicalExercises: [
          'Identify complex chart patterns',
          'Use multiple timeframe analysis',
          'Combine multiple indicators'
        ],
        resources: intermediateCourses.slice(0, 4).map(c => c.url),
        estimatedTime: '3 weeks'
      },
      {
        id: generateId('module'),
        title: 'Trading Strategies',
        description: 'Develop and test trading strategies',
        concepts: ['strategy development', 'backtesting', 'optimization'],
        practicalExercises: [
          'Create a trading strategy',
          'Backtest strategy performance',
          'Optimize strategy parameters'
        ],
        resources: intermediateCourses.filter(c => c.topics.includes('strategy')).map(c => c.url),
        estimatedTime: '4 weeks'
      }
    ];

    return {
      id: generateId('path'),
      name: 'Intermediate Trading Skills',
      description: 'Advanced techniques for developing traders',
      level: 'intermediate',
      estimatedDuration: '7-8 weeks',
      modules
    };
  }

  private createAdvancedPath(courses: TradingCourse[], concepts: TradingConcept[]): LearningPath {
    const advancedCourses = courses.filter(c => c.level === 'advanced');
    const advancedConcepts = concepts.filter(c => c.difficulty === 'advanced');

    const modules: LearningModule[] = [
      {
        id: generateId('module'),
        title: 'Professional Trading',
        description: 'Advanced strategies and portfolio management',
        concepts: advancedConcepts.slice(0, 6).map(c => c.name),
        practicalExercises: [
          'Develop algorithmic trading strategies',
          'Manage multiple trading accounts',
          'Implement advanced risk models'
        ],
        resources: advancedCourses.slice(0, 3).map(c => c.url),
        estimatedTime: '4 weeks'
      }
    ];

    return {
      id: generateId('path'),
      name: 'Advanced Trading Mastery',
      description: 'Professional-level trading skills',
      level: 'advanced',
      estimatedDuration: '4-6 weeks',
      modules
    };
  }

  private createSpecializedPath(name: string, courses: TradingCourse[], concepts: TradingConcept[], topic: string): LearningPath {
    const specializedCourses = courses.filter(c => 
      c.topics.some(t => t.toLowerCase().includes(topic.toLowerCase()))
    );
    
    const specializedConcepts = concepts.filter(c => 
      c.category.toLowerCase().includes(topic.toLowerCase()) ||
      c.name.toLowerCase().includes(topic.toLowerCase())
    );

    const modules: LearningModule[] = [
      {
        id: generateId('module'),
        title: `${name} Fundamentals`,
        description: `Basic concepts specific to ${name.toLowerCase()}`,
        concepts: specializedConcepts.slice(0, 5).map(c => c.name),
        practicalExercises: [
          `Understand ${topic} market structure`,
          `Practice ${topic} analysis techniques`,
          `Develop ${topic} trading strategies`
        ],
        resources: specializedCourses.slice(0, 3).map(c => c.url),
        estimatedTime: '3 weeks'
      }
    ];

    return {
      id: generateId('path'),
      name,
      description: `Specialized learning path for ${name.toLowerCase()}`,
      level: 'intermediate',
      estimatedDuration: '3-4 weeks',
      modules
    };
  }

  extractTradingStrategies(courses: TradingCourse[], concepts: TradingConcept[]): TradingStrategy[] {
    console.log('⚡ Extracting trading strategies...');

    const strategies: TradingStrategy[] = [];

    // Extract strategies from course titles and descriptions
    const strategyKeywords = [
      'scalping', 'day trading', 'swing trading', 'position trading',
      'breakout', 'reversal', 'trend following', 'mean reversion',
      'momentum', 'arbitrage', 'grid trading', 'martingale'
    ];

    strategyKeywords.forEach(keyword => {
      const relevantCourses = courses.filter(c => 
        c.title.toLowerCase().includes(keyword) || 
        c.description.toLowerCase().includes(keyword) ||
        c.topics.some(t => t.toLowerCase().includes(keyword))
      );

      if (relevantCourses.length > 0) {
        const strategy: TradingStrategy = {
          name: this.capitalizeWords(keyword),
          description: {
            english: `${this.capitalizeWords(keyword)} trading strategy`,
            hindi: `${this.capitalizeWords(keyword)} ट्रेडिंग रणनीति`,
            hindiEnglish: `${this.capitalizeWords(keyword)} trading strategy (ट्रेडिंग रणनीति)`
          },
          steps: this.extractStrategySteps(relevantCourses),
          riskLevel: this.determineRiskLevel(keyword),
          timeframe: this.determineTimeframe(keyword),
          markets: this.extractMarkets(relevantCourses),
          indicators: this.extractIndicators(relevantCourses)
        };

        strategies.push(strategy);
      }
    });

    return strategies;
  }

  private capitalizeWords(str: string): string {
    return str.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  private extractStrategySteps(courses: TradingCourse[]): string[] {
    // This would be enhanced with NLP to extract actual steps
    return [
      'Identify market conditions',
      'Set entry criteria',
      'Determine position size',
      'Set stop loss and take profit',
      'Monitor and manage the trade'
    ];
  }

  private determineRiskLevel(strategy: string): 'low' | 'medium' | 'high' {
    const highRisk = ['scalping', 'day trading', 'martingale'];
    const lowRisk = ['position trading', 'swing trading'];
    
    if (highRisk.includes(strategy)) return 'high';
    if (lowRisk.includes(strategy)) return 'low';
    return 'medium';
  }

  private determineTimeframe(strategy: string): string {
    const timeframes: Record<string, string> = {
      'scalping': '1-5 minutes',
      'day trading': '5 minutes - 1 hour',
      'swing trading': '1 hour - 1 day',
      'position trading': '1 day - several weeks'
    };
    
    return timeframes[strategy] || 'Variable';
  }

  private extractMarkets(courses: TradingCourse[]): string[] {
    const markets = new Set<string>();
    
    courses.forEach(course => {
      course.topics.forEach(topic => {
        if (['forex', 'stocks', 'crypto', 'cryptocurrency', 'options', 'futures'].includes(topic.toLowerCase())) {
          markets.add(topic);
        }
      });
    });
    
    return Array.from(markets);
  }

  private extractIndicators(courses: TradingCourse[]): string[] {
    const indicators = new Set<string>();
    const indicatorKeywords = [
      'moving average', 'rsi', 'macd', 'bollinger bands', 'fibonacci',
      'stochastic', 'cci', 'atr', 'volume', 'momentum'
    ];
    
    courses.forEach(course => {
      const allText = (course.title + ' ' + course.description).toLowerCase();
      indicatorKeywords.forEach(indicator => {
        if (allText.includes(indicator)) {
          indicators.add(indicator);
        }
      });
    });
    
    return Array.from(indicators);
  }
}
