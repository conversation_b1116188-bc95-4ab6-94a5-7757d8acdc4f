import { YouTubeScraper } from './youtube-scraper';
import { WebScraper } from './web-scraper';
import { TradingCourse, TradingConcept } from '../types';
import { saveJsonData, logProgress } from '../utils/helpers';
import * as path from 'path';

export class MasterScraper {
  private youtubeScraper: YouTubeScraper;
  private webScraper: WebScraper;

  constructor() {
    this.youtubeScraper = new YouTubeScraper({
      maxPages: 5,
      delayBetweenRequests: 2000,
      headless: true
    });
    
    this.webScraper = new WebScraper({
      maxPages: 10,
      delayBetweenRequests: 1000
    });
  }

  async scrapeAllSources(): Promise<{
    courses: TradingCourse[];
    concepts: TradingConcept[];
  }> {
    console.log('🚀 Starting comprehensive trading education scraping...');
    
    const allCourses: TradingCourse[] = [];
    const allConcepts: TradingConcept[] = [];

    try {
      // Initialize YouTube scraper
      await this.youtubeScraper.initialize();
      
      // 1. Scrape YouTube courses
      console.log('\n📺 Scraping YouTube courses...');
      const youtubeQueries = [
        'best trading course',
        'forex trading tutorial',
        'stock trading for beginners',
        'cryptocurrency trading guide',
        'day trading strategies',
        'technical analysis course',
        'trading psychology',
        'risk management trading'
      ];

      for (let i = 0; i < youtubeQueries.length; i++) {
        const query = youtubeQueries[i];
        logProgress(`Scraping YouTube: ${query}`, i + 1, youtubeQueries.length);
        
        try {
          const courses = await this.youtubeScraper.scrapeYouTubeCourses(query);
          allCourses.push(...courses);
          console.log(`  ✅ Found ${courses.length} courses for "${query}"`);
        } catch (error) {
          console.error(`  ❌ Error scraping "${query}":`, error);
        }
      }

      // 2. Scrape trading websites
      console.log('\n🌐 Scraping trading education websites...');
      try {
        const webCourses = await this.webScraper.scrapeTradingWebsites();
        allCourses.push(...webCourses);
        console.log(`  ✅ Found ${webCourses.length} courses from websites`);
      } catch (error) {
        console.error('  ❌ Error scraping websites:', error);
      }

      // 3. Scrape trading concepts
      console.log('\n📚 Scraping trading concepts...');
      try {
        const concepts = await this.webScraper.scrapeTradingConcepts();
        allConcepts.push(...concepts);
        console.log(`  ✅ Found ${concepts.length} trading concepts`);
      } catch (error) {
        console.error('  ❌ Error scraping concepts:', error);
      }

      // 4. Save raw data
      console.log('\n💾 Saving scraped data...');
      await this.saveScrapedData(allCourses, allConcepts);

      console.log('\n✨ Scraping completed successfully!');
      console.log(`📊 Total courses found: ${allCourses.length}`);
      console.log(`📖 Total concepts found: ${allConcepts.length}`);

    } catch (error) {
      console.error('❌ Error during scraping:', error);
    } finally {
      // Clean up
      await this.youtubeScraper.close();
    }

    return {
      courses: allCourses,
      concepts: allConcepts
    };
  }

  private async saveScrapedData(courses: TradingCourse[], concepts: TradingConcept[]): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    
    // Save courses
    const coursesPath = path.join('data', 'raw', `courses_${timestamp}.json`);
    await saveJsonData(courses, coursesPath);
    console.log(`  ✅ Courses saved to: ${coursesPath}`);

    // Save concepts
    const conceptsPath = path.join('data', 'raw', `concepts_${timestamp}.json`);
    await saveJsonData(concepts, conceptsPath);
    console.log(`  ✅ Concepts saved to: ${conceptsPath}`);

    // Save combined data
    const combinedData = {
      metadata: {
        scrapedAt: new Date(),
        totalCourses: courses.length,
        totalConcepts: concepts.length,
        sources: ['YouTube', 'Investopedia', 'BabyPips']
      },
      courses,
      concepts
    };

    const combinedPath = path.join('data', 'raw', `trading_data_${timestamp}.json`);
    await saveJsonData(combinedData, combinedPath);
    console.log(`  ✅ Combined data saved to: ${combinedPath}`);
  }

  async scrapeSpecificSources(sources: {
    youtubeQueries?: string[];
    youtubePlaylistUrls?: string[];
    websiteUrls?: string[];
  }): Promise<TradingCourse[]> {
    const courses: TradingCourse[] = [];

    if (sources.youtubeQueries || sources.youtubePlaylistUrls) {
      await this.youtubeScraper.initialize();
    }

    try {
      // Scrape specific YouTube queries
      if (sources.youtubeQueries) {
        for (const query of sources.youtubeQueries) {
          console.log(`Scraping YouTube query: ${query}`);
          const queryCourses = await this.youtubeScraper.scrapeYouTubeCourses(query);
          courses.push(...queryCourses);
        }
      }

      // Scrape specific YouTube playlists
      if (sources.youtubePlaylistUrls) {
        for (const playlistUrl of sources.youtubePlaylistUrls) {
          console.log(`Scraping YouTube playlist: ${playlistUrl}`);
          const playlistCourses = await this.youtubeScraper.scrapeYouTubePlaylist(playlistUrl);
          courses.push(...playlistCourses);
        }
      }

    } finally {
      await this.youtubeScraper.close();
    }

    return courses;
  }
}

// CLI interface for running scraper
async function main() {
  const scraper = new MasterScraper();
  
  try {
    const result = await scraper.scrapeAllSources();
    
    console.log('\n📈 Scraping Summary:');
    console.log(`Total courses: ${result.courses.length}`);
    console.log(`Total concepts: ${result.concepts.length}`);
    
    // Show platform distribution
    const platformCounts = result.courses.reduce((acc, course) => {
      acc[course.platform] = (acc[course.platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n📊 Platform Distribution:');
    Object.entries(platformCounts).forEach(([platform, count]) => {
      console.log(`  ${platform}: ${count} courses`);
    });

    // Show difficulty distribution
    const difficultyCount = result.courses.reduce((acc, course) => {
      acc[course.level] = (acc[course.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n📚 Difficulty Distribution:');
    Object.entries(difficultyCount).forEach(([level, count]) => {
      console.log(`  ${level}: ${count} courses`);
    });

  } catch (error) {
    console.error('❌ Scraping failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
