import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { TradingCourse, ScrapingConfig } from '../types';
import { delay, sanitizeText } from '../utils/helpers';

export class YouTubeScraper {
  private browser: Browser | null = null;
  private config: ScrapingConfig;

  constructor(config: Partial<ScrapingConfig> = {}) {
    this.config = {
      maxPages: 5,
      delayBetweenRequests: 2000,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      headless: true,
      timeout: 30000,
      ...config
    };
  }

  async initialize(): Promise<void> {
    this.browser = await chromium.launch({ 
      headless: this.config.headless,
      timeout: this.config.timeout 
    });
  }

  async scrapeYouTubeCourses(searchQuery: string = 'best trading course'): Promise<TradingCourse[]> {
    if (!this.browser) {
      throw new Error('Browser not initialized. Call initialize() first.');
    }

    const page = await this.browser.newPage({
      userAgent: this.config.userAgent
    });

    const courses: TradingCourse[] = [];
    
    try {
      // Navigate to YouTube search
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(searchQuery)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle' });

      // Wait for video results to load
      await page.waitForSelector('ytd-video-renderer', { timeout: 10000 });

      // Extract video information
      const videoElements = await page.$$('ytd-video-renderer');
      
      for (let i = 0; i < Math.min(videoElements.length, 20); i++) {
        const element = videoElements[i];
        
        try {
          const titleElement = await element.$('#video-title');
          const channelElement = await element.$('#text a');
          const durationElement = await element.$('.ytd-thumbnail-overlay-time-status-renderer');
          const viewsElement = await element.$('#metadata-line span:first-child');
          const linkElement = await element.$('#video-title');

          const title = await titleElement?.textContent() || '';
          const channel = await channelElement?.textContent() || '';
          const duration = await durationElement?.textContent() || '';
          const views = await viewsElement?.textContent() || '';
          const href = await linkElement?.getAttribute('href') || '';

          if (title && channel && href) {
            const course: TradingCourse = {
              id: `youtube_${Date.now()}_${i}`,
              title: sanitizeText(title),
              description: `Trading course video by ${sanitizeText(channel)}`,
              instructor: sanitizeText(channel),
              platform: 'YouTube',
              url: `https://www.youtube.com${href}`,
              duration: sanitizeText(duration),
              level: this.determineDifficultyLevel(title),
              topics: this.extractTopics(title),
              reviews: this.parseViews(views),
              price: 'Free',
              language: 'English',
              scrapedAt: new Date()
            };

            courses.push(course);
          }
        } catch (error) {
          console.warn(`Error extracting video ${i}:`, error);
        }

        // Add delay between extractions
        await delay(500);
      }

    } catch (error) {
      console.error('Error scraping YouTube:', error);
    } finally {
      await page.close();
    }

    return courses;
  }

  async scrapeYouTubePlaylist(playlistUrl: string): Promise<TradingCourse[]> {
    if (!this.browser) {
      throw new Error('Browser not initialized. Call initialize() first.');
    }

    const page = await this.browser.newPage({
      userAgent: this.config.userAgent
    });

    const courses: TradingCourse[] = [];

    try {
      await page.goto(playlistUrl, { waitUntil: 'networkidle' });
      
      // Wait for playlist videos to load
      await page.waitForSelector('ytd-playlist-video-renderer', { timeout: 10000 });

      const videoElements = await page.$$('ytd-playlist-video-renderer');

      for (let i = 0; i < videoElements.length; i++) {
        const element = videoElements[i];
        
        try {
          const titleElement = await element.$('#video-title');
          const durationElement = await element.$('.ytd-thumbnail-overlay-time-status-renderer');
          const linkElement = await element.$('#video-title');

          const title = await titleElement?.textContent() || '';
          const duration = await durationElement?.textContent() || '';
          const href = await linkElement?.getAttribute('href') || '';

          if (title && href) {
            const course: TradingCourse = {
              id: `playlist_${Date.now()}_${i}`,
              title: sanitizeText(title),
              description: `Trading course video from playlist`,
              instructor: 'Playlist Creator',
              platform: 'YouTube',
              url: `https://www.youtube.com${href}`,
              duration: sanitizeText(duration),
              level: this.determineDifficultyLevel(title),
              topics: this.extractTopics(title),
              price: 'Free',
              language: 'English',
              scrapedAt: new Date()
            };

            courses.push(course);
          }
        } catch (error) {
          console.warn(`Error extracting playlist video ${i}:`, error);
        }

        await delay(300);
      }

    } catch (error) {
      console.error('Error scraping YouTube playlist:', error);
    } finally {
      await page.close();
    }

    return courses;
  }

  private determineDifficultyLevel(title: string): 'beginner' | 'intermediate' | 'advanced' {
    const lowerTitle = title.toLowerCase();
    
    if (lowerTitle.includes('beginner') || lowerTitle.includes('basic') || 
        lowerTitle.includes('start') || lowerTitle.includes('intro')) {
      return 'beginner';
    }
    
    if (lowerTitle.includes('advanced') || lowerTitle.includes('expert') || 
        lowerTitle.includes('professional') || lowerTitle.includes('master')) {
      return 'advanced';
    }
    
    return 'intermediate';
  }

  private extractTopics(title: string): string[] {
    const topics: string[] = [];
    const lowerTitle = title.toLowerCase();
    
    const topicKeywords = [
      'forex', 'stocks', 'crypto', 'options', 'futures', 'day trading',
      'swing trading', 'scalping', 'technical analysis', 'fundamental analysis',
      'chart patterns', 'indicators', 'risk management', 'psychology',
      'strategy', 'market', 'investment', 'portfolio'
    ];

    topicKeywords.forEach(keyword => {
      if (lowerTitle.includes(keyword)) {
        topics.push(keyword);
      }
    });

    return topics;
  }

  private parseViews(viewsText: string): number {
    const match = viewsText.match(/(\d+(?:,\d+)*)/);
    if (match) {
      return parseInt(match[1].replace(/,/g, ''));
    }
    return 0;
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
