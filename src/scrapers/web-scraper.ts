import axios from 'axios';
import * as cheerio from 'cheerio';
import { TradingCourse, TradingConcept, ScrapingConfig } from '../types';
import { delay, sanitizeText, extractKeywords, generateId } from '../utils/helpers';

export class WebScraper {
  private config: ScrapingConfig;

  constructor(config: Partial<ScrapingConfig> = {}) {
    this.config = {
      maxPages: 10,
      delayBetweenRequests: 1000,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      headless: true,
      timeout: 30000,
      ...config
    };
  }

  async scrapeTradingWebsites(): Promise<TradingCourse[]> {
    const courses: TradingCourse[] = [];
    
    // List of trading education websites to scrape
    const websites = [
      {
        name: 'Investopedia',
        baseUrl: 'https://www.investopedia.com',
        searchPath: '/search?q=trading+course'
      },
      {
        name: 'BabyPip<PERSON>',
        baseUrl: 'https://www.babypips.com',
        searchPath: '/learn/forex'
      }
    ];

    for (const website of websites) {
      try {
        console.log(`Scraping ${website.name}...`);
        const siteCourses = await this.scrapeWebsite(website);
        courses.push(...siteCourses);
        
        await delay(this.config.delayBetweenRequests);
      } catch (error) {
        console.error(`Error scraping ${website.name}:`, error);
      }
    }

    return courses;
  }

  private async scrapeWebsite(website: { name: string; baseUrl: string; searchPath: string }): Promise<TradingCourse[]> {
    const courses: TradingCourse[] = [];
    
    try {
      const response = await axios.get(`${website.baseUrl}${website.searchPath}`, {
        headers: {
          'User-Agent': this.config.userAgent
        },
        timeout: this.config.timeout
      });

      const $ = cheerio.load(response.data);
      
      // Generic selectors for course/article content
      const contentSelectors = [
        'article',
        '.course-item',
        '.lesson-item',
        '.tutorial-item',
        '.content-item',
        'h2, h3, h4',
        '.title'
      ];

      for (const selector of contentSelectors) {
        $(selector).each((index, element) => {
          try {
            const $element = $(element);
            const title = $element.find('h1, h2, h3, h4, .title').first().text() ||
                         $element.text().substring(0, 100);
            
            if (title && this.isRelevantContent(title)) {
              const link = $element.find('a').first().attr('href') || '';
              const description = $element.find('p, .description, .summary').first().text() || '';
              
              const course: TradingCourse = {
                id: generateId('web'),
                title: sanitizeText(title),
                description: sanitizeText(description),
                instructor: website.name,
                platform: website.name,
                url: this.resolveUrl(link, website.baseUrl),
                duration: 'Variable',
                level: this.determineDifficultyLevel(title + ' ' + description),
                topics: extractKeywords(title + ' ' + description),
                price: 'Free',
                language: 'English',
                scrapedAt: new Date()
              };

              courses.push(course);
            }
          } catch (error) {
            console.warn(`Error extracting content from ${website.name}:`, error);
          }
        });
      }

    } catch (error) {
      console.error(`Error fetching ${website.name}:`, error);
    }

    return courses.slice(0, 20); // Limit results
  }

  async scrapeTradingConcepts(): Promise<TradingConcept[]> {
    const concepts: TradingConcept[] = [];
    
    // Scrape trading concepts from educational websites
    const conceptSources = [
      'https://www.investopedia.com/trading-4427765',
      'https://www.babypips.com/learn/forex/what-is-forex-trading'
    ];

    for (const url of conceptSources) {
      try {
        console.log(`Scraping concepts from ${url}...`);
        const siteConcepts = await this.scrapeConceptsFromPage(url);
        concepts.push(...siteConcepts);
        
        await delay(this.config.delayBetweenRequests);
      } catch (error) {
        console.error(`Error scraping concepts from ${url}:`, error);
      }
    }

    return concepts;
  }

  private async scrapeConceptsFromPage(url: string): Promise<TradingConcept[]> {
    const concepts: TradingConcept[] = [];
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': this.config.userAgent
        },
        timeout: this.config.timeout
      });

      const $ = cheerio.load(response.data);
      
      // Extract definitions and concepts
      $('h2, h3, h4').each((index, element) => {
        const $element = $(element);
        const title = $element.text().trim();
        
        if (this.isRelevantConcept(title)) {
          const nextParagraph = $element.next('p').text();
          const definition = sanitizeText(nextParagraph);
          
          if (definition.length > 50) {
            const concept: TradingConcept = {
              id: generateId('concept'),
              name: sanitizeText(title),
              category: this.categorizeTrading(title),
              definition: definition,
              examples: this.extractExamples(nextParagraph),
              difficulty: this.determineDifficultyLevel(title + ' ' + definition),
              relatedConcepts: extractKeywords(title + ' ' + definition),
              sources: [url]
            };

            concepts.push(concept);
          }
        }
      });

    } catch (error) {
      console.error(`Error scraping concepts from ${url}:`, error);
    }

    return concepts;
  }

  private isRelevantContent(text: string): boolean {
    const lowerText = text.toLowerCase();
    const relevantKeywords = [
      'trading', 'forex', 'stock', 'crypto', 'investment', 'market',
      'strategy', 'analysis', 'course', 'tutorial', 'guide', 'learn'
    ];
    
    return relevantKeywords.some(keyword => lowerText.includes(keyword)) &&
           text.length > 10 && text.length < 200;
  }

  private isRelevantConcept(text: string): boolean {
    const lowerText = text.toLowerCase();
    const conceptKeywords = [
      'what is', 'how to', 'definition', 'meaning', 'explained',
      'trading', 'forex', 'stock', 'market', 'analysis', 'strategy'
    ];
    
    return conceptKeywords.some(keyword => lowerText.includes(keyword));
  }

  private determineDifficultyLevel(text: string): 'beginner' | 'intermediate' | 'advanced' {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('beginner') || lowerText.includes('basic') || 
        lowerText.includes('introduction') || lowerText.includes('start') ||
        lowerText.includes('fundamentals')) {
      return 'beginner';
    }
    
    if (lowerText.includes('advanced') || lowerText.includes('expert') || 
        lowerText.includes('professional') || lowerText.includes('complex') ||
        lowerText.includes('sophisticated')) {
      return 'advanced';
    }
    
    return 'intermediate';
  }

  private categorizeTrading(text: string): string {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('forex') || lowerText.includes('currency')) {
      return 'forex';
    }
    if (lowerText.includes('stock') || lowerText.includes('equity')) {
      return 'stocks';
    }
    if (lowerText.includes('crypto') || lowerText.includes('bitcoin')) {
      return 'cryptocurrency';
    }
    if (lowerText.includes('option')) {
      return 'options';
    }
    if (lowerText.includes('future')) {
      return 'futures';
    }
    if (lowerText.includes('technical') || lowerText.includes('chart')) {
      return 'technical_analysis';
    }
    if (lowerText.includes('fundamental')) {
      return 'fundamental_analysis';
    }
    if (lowerText.includes('risk') || lowerText.includes('management')) {
      return 'risk_management';
    }
    if (lowerText.includes('psychology') || lowerText.includes('emotion')) {
      return 'psychology';
    }
    
    return 'general';
  }

  private extractExamples(text: string): string[] {
    const examples: string[] = [];
    const sentences = text.split(/[.!?]+/);
    
    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase().trim();
      if (lowerSentence.includes('example') || lowerSentence.includes('for instance') ||
          lowerSentence.includes('such as') || lowerSentence.includes('like')) {
        examples.push(sanitizeText(sentence));
      }
    });
    
    return examples.slice(0, 3); // Limit to 3 examples
  }

  private resolveUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http')) {
      return url;
    }
    if (url.startsWith('/')) {
      return baseUrl + url;
    }
    return baseUrl + '/' + url;
  }
}
