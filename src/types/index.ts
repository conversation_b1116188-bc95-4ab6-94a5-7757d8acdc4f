// Core data structures for trading education analysis

export interface TradingCourse {
  id: string;
  title: string;
  description: string;
  instructor: string;
  platform: string;
  url: string;
  duration: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  topics: string[];
  rating?: number;
  reviews?: number;
  price?: string;
  language: string;
  scrapedAt: Date;
}

export interface TradingConcept {
  id: string;
  name: string;
  category: string;
  definition: string;
  examples: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  relatedConcepts: string[];
  sources: string[];
}

export interface AnalysisResult {
  totalCourses: number;
  conceptsExtracted: number;
  topicDistribution: Record<string, number>;
  difficultyDistribution: Record<string, number>;
  platformDistribution: Record<string, number>;
  averageRating: number;
  recommendations: string[];
  generatedAt: Date;
}

export interface DocumentContent {
  title: string;
  sections: DocumentSection[];
  language: 'english' | 'hindi' | 'hindi-english';
  metadata: {
    generatedAt: Date;
    version: string;
    totalPages?: number;
  };
}

export interface DocumentSection {
  title: string;
  content: string;
  subsections?: DocumentSection[];
  type: 'introduction' | 'concept' | 'strategy' | 'example' | 'conclusion';
}

export interface ScrapingConfig {
  maxPages: number;
  delayBetweenRequests: number;
  userAgent: string;
  headless: boolean;
  timeout: number;
}

export interface LanguageContent {
  english: string;
  hindi: string;
  hindiEnglish: string;
}

export interface TradingStrategy {
  name: string;
  description: LanguageContent;
  steps: string[];
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  markets: string[];
  indicators: string[];
}

export interface LearningPath {
  id: string;
  name: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: string;
  modules: LearningModule[];
}

export interface LearningModule {
  id: string;
  title: string;
  description: string;
  concepts: string[];
  practicalExercises: string[];
  resources: string[];
  estimatedTime: string;
}
