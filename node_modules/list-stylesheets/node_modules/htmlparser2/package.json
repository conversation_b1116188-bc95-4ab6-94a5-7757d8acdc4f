{"name": "htmlparser2", "description": "Fast & forgiving HTML/XML/RSS parser", "version": "3.10.1", "author": "<PERSON> <<EMAIL>>", "keywords": ["html", "parser", "streams", "xml", "dom", "rss", "feed", "atom"], "repository": {"type": "git", "url": "git://github.com/fb55/htmlparser2.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/fb55/htmlparser2/issues"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "files": ["lib"], "scripts": {"lcov": "istanbul cover _mocha --report lcovonly -- -R spec", "coveralls": "npm run lint && npm run lcov && (cat coverage/lcov.info | coveralls || exit 0)", "test": "mocha && npm run lint", "lint": "eslint lib test"}, "dependencies": {"domelementtype": "^1.3.1", "domhandler": "^2.3.0", "domutils": "^1.5.1", "entities": "^1.1.1", "inherits": "^2.0.1", "readable-stream": "^3.1.1"}, "devDependencies": {"coveralls": "^3.0.1", "eslint": "^5.13.0", "istanbul": "^0.4.3", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0"}, "browser": {"readable-stream": false}, "license": "MIT", "prettier": {"tabWidth": 4}}