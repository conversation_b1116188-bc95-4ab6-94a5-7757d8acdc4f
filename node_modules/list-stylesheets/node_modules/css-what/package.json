{"author": "<PERSON> <<EMAIL>> (http://feedic.com)", "name": "css-what", "description": "a CSS selector parser", "version": "2.1.3", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "undef": true, "unused": true, "eqnull": true, "proto": true, "node": true, "globals": {"describe": true, "it": true}}, "prettier": {"tabWidth": 4}}