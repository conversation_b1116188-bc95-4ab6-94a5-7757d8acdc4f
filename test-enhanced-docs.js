// Test script to generate enhanced documents
const { DocumentGenerator } = require('./dist/generators/document-generator.js');

async function testEnhancedDocuments() {
  console.log('🧪 Testing Enhanced Document Generation...');
  
  try {
    const generator = new DocumentGenerator();
    
    // Create sample data for testing
    const sampleCourses = [
      {
        id: 'test1',
        title: 'Complete Forex Trading Course',
        description: 'Learn forex trading from basics to advanced',
        instructor: 'Trading Expert',
        platform: 'YouTube',
        url: 'https://example.com/course1',
        duration: '10 hours',
        level: 'beginner',
        topics: ['forex', 'technical analysis', 'risk management'],
        rating: 4.5,
        reviews: 1000,
        price: 'Free',
        language: 'English',
        scrapedAt: new Date()
      }
    ];
    
    const sampleConcepts = [
      {
        id: 'concept1',
        name: 'Support and Resistance',
        category: 'technical_analysis',
        definition: 'Key price levels where buying or selling pressure is concentrated',
        examples: ['Previous highs and lows', 'Psychological levels'],
        difficulty: 'beginner',
        relatedConcepts: ['trend lines', 'breakouts'],
        sources: ['https://example.com']
      }
    ];
    
    const sampleLearningPaths = [
      {
        id: 'path1',
        name: 'Beginner Trading Path',
        description: 'Complete foundation for new traders',
        level: 'beginner',
        estimatedDuration: '8 weeks',
        modules: [
          {
            id: 'module1',
            title: 'Trading Basics',
            description: 'Fundamental concepts',
            concepts: ['support', 'resistance', 'trends'],
            practicalExercises: ['Demo trading', 'Chart analysis'],
            resources: ['https://example.com'],
            estimatedTime: '2 weeks'
          }
        ]
      }
    ];
    
    const sampleStrategies = [
      {
        name: 'Moving Average Crossover',
        description: {
          english: 'Simple trend following strategy using moving averages',
          hindi: 'मूविंग एवरेज का उपयोग करके ट्रेंड फॉलोइंग रणनीति',
          hindiEnglish: 'Moving average का use करके trend following strategy'
        },
        steps: ['Identify trend', 'Wait for crossover', 'Enter trade'],
        riskLevel: 'medium',
        timeframe: '1H-4H',
        markets: ['forex', 'stocks'],
        indicators: ['moving average', 'volume']
      }
    ];
    
    const sampleAnalysis = {
      totalCourses: 150,
      conceptsExtracted: 75,
      topicDistribution: {
        'forex': 45,
        'technical analysis': 38,
        'day trading': 32,
        'risk management': 28,
        'cryptocurrency': 25
      },
      difficultyDistribution: {
        'beginner': 60,
        'intermediate': 70,
        'advanced': 20
      },
      platformDistribution: {
        'YouTube': 100,
        'Investopedia': 30,
        'BabyPips': 20
      },
      averageRating: 4.2,
      recommendations: [
        'More beginner-friendly content is needed',
        'Risk management should be emphasized more',
        'Trading psychology needs more coverage'
      ],
      generatedAt: new Date()
    };
    
    // Generate only English document for testing
    console.log('📄 Generating enhanced English document...');
    await generator.generateComprehensiveDocument(
      sampleCourses,
      sampleConcepts,
      sampleLearningPaths,
      sampleStrategies,
      sampleAnalysis
    );
    
    console.log('✅ Enhanced document generation completed!');
    console.log('📁 Check the output/ directory for the new comprehensive documents');
    console.log('');
    console.log('🎉 The documents now include:');
    console.log('   • Complete Table of Contents');
    console.log('   • 14 comprehensive chapters');
    console.log('   • Detailed explanations with examples');
    console.log('   • Step-by-step learning progression');
    console.log('   • Practical trading strategies');
    console.log('   • Risk management techniques');
    console.log('   • Trading psychology guidance');
    console.log('   • Common mistakes and solutions');
    console.log('   • Long-term success principles');
    console.log('   • Extensive resource lists');
    console.log('');
    console.log('📖 This is now a true comprehensive trading education guide!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testEnhancedDocuments();
