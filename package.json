{"name": "trading-education-analysis", "version": "1.0.0", "description": "Comprehensive trading education research and analysis system with multi-language document generation", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc && node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "scrape": "tsc && node dist/scrapers/index.js", "analyze": "tsc && node dist/analyzers/index.js", "generate": "tsc && node dist/generators/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["trading", "education", "analysis", "web-scraping", "document-generation", "multi-language", "hindi", "english"], "author": "", "license": "ISC", "dependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^22.15.29", "axios": "^1.9.0", "cheerio": "^1.0.0", "docx": "^9.5.0", "fs-extra": "^11.3.0", "html-pdf-node": "^1.0.8", "jspdf": "^3.0.1", "playwright": "^1.52.0", "puppeteer": "^24.9.0", "typescript": "^5.8.3"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.28.0"}}