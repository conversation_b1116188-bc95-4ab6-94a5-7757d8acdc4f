import { TradingCourse, TradingConcept, LearningPath, TradingStrategy, AnalysisResult } from '../types';
export declare class DocumentGenerator {
    private chartGenerator;
    private diagramGenerator;
    constructor();
    generateComprehensiveDocument(courses: TradingCourse[], concepts: TradingConcept[], learningPaths: LearningPath[], strategies: TradingStrategy[], analysis: AnalysisResult): Promise<void>;
    private generateEnglishDocument;
    private generateHindiDocument;
    private generateHindiEnglishDocument;
    private createEnglishContent;
    private createHindiContent;
    private createAdvancedHindiSections;
    private createHindiEnglishContent;
    private generateEnhancedDocx;
    private createEnhancedSection;
    private createEnhancedParagraph;
    private generatePdf;
    private createHtmlFromContent;
    private formatConceptsForEnglish;
    private formatConceptsForHindi;
    private formatLearningPathsForEnglish;
    private formatLearningPathsForHindi;
    private formatStrategiesForEnglish;
    private formatStrategiesForHindi;
    private formatCoursesForEnglish;
    private formatCoursesForHindi;
    private createAdvancedSections;
    private translateToHindi;
    private createDiagramReference;
    private createChartReference;
    private createTableHeader;
    private extractTableLines;
    private createTable;
    private createTableAsParagraphs;
    private generateEnhancedPdf;
    private createEnhancedHtmlFromContent;
    private convertTableToHtml;
    private createHtmlTable;
}
//# sourceMappingURL=document-generator.d.ts.map