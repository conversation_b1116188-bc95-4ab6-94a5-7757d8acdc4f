import { TradingCourse, TradingConcept, LearningPath, TradingStrategy, AnalysisResult } from '../types';
export declare class DocumentGenerator {
    generateComprehensiveDocument(courses: TradingCourse[], concepts: TradingConcept[], learningPaths: LearningPath[], strategies: TradingStrategy[], analysis: AnalysisResult): Promise<void>;
    private generateEnglishDocument;
    private generateHindiDocument;
    private generateHindiEnglishDocument;
    private createEnglishContent;
    private createHindiContent;
    private createHindiEnglishContent;
    private generateDocx;
    private generatePdf;
    private createHtmlFromContent;
    private formatConceptsForEnglish;
    private formatConceptsForHindi;
    private formatLearningPathsForEnglish;
    private formatLearningPathsForHindi;
    private formatStrategiesForEnglish;
    private formatStrategiesForHindi;
    private formatCoursesForEnglish;
    private formatCoursesForHindi;
    private createAdvancedSections;
    private translateToHindi;
}
//# sourceMappingURL=document-generator.d.ts.map