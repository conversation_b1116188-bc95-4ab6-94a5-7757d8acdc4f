export declare class DiagramGenerator {
    private outputDir;
    constructor();
    generateTradingDiagrams(): Promise<{
        [key: string]: string;
    }>;
    private generateTradingBasicsDiagram;
    private generateMarketStructureDiagram;
    private generateRiskManagementDiagram;
    private generateOrderTypesDiagram;
    private generateTradingPsychologyDiagram;
    private generateLearningPathDiagram;
    private generateTradingPlatformDiagram;
    private generateMarketSessionsDiagram;
}
//# sourceMappingURL=diagram-generator.d.ts.map