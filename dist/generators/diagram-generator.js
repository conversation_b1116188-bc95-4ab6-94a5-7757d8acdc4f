"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagramGenerator = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const helpers_1 = require("../utils/helpers");
class DiagramGenerator {
    constructor() {
        this.outputDir = path.join('output', 'diagrams');
    }
    async generateTradingDiagrams() {
        await (0, helpers_1.ensureDirectoryExists)(this.outputDir);
        const diagrams = {};
        // Generate various educational diagrams
        diagrams.tradingBasics = await this.generateTradingBasicsDiagram();
        diagrams.marketStructure = await this.generateMarketStructureDiagram();
        diagrams.orderTypes = await this.generateOrderTypesDiagram();
        diagrams.riskManagement = await this.generateRiskManagementDiagram();
        diagrams.tradingPsychology = await this.generateTradingPsychologyDiagram();
        diagrams.learningPath = await this.generateLearningPathDiagram();
        diagrams.tradingPlatform = await this.generateTradingPlatformDiagram();
        diagrams.marketSessions = await this.generateMarketSessionsDiagram();
        return diagrams;
    }
    async generateTradingBasicsDiagram() {
        const svgContent = `
    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .title { font: bold 24px sans-serif; fill: #2c3e50; }
          .subtitle { font: bold 18px sans-serif; fill: #34495e; }
          .text { font: 14px sans-serif; fill: #2c3e50; }
          .box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
          .arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
          .buy-box { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; }
          .sell-box { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
        </marker>
      </defs>
      
      <!-- Title -->
      <text x="400" y="30" text-anchor="middle" class="title">Trading Basics: Buy Low, Sell High</text>
      
      <!-- Price Chart Background -->
      <rect x="50" y="80" width="700" height="300" class="box" />
      <text x="400" y="105" text-anchor="middle" class="subtitle">Price Movement Over Time</text>
      
      <!-- Price Line -->
      <polyline points="80,300 150,250 220,280 290,200 360,230 430,180 500,220 570,160 640,190 720,140" 
                stroke="#3498db" stroke-width="3" fill="none" />
      
      <!-- Buy Points -->
      <circle cx="150" cy="250" r="8" fill="#27ae60" />
      <text x="150" y="275" text-anchor="middle" class="text">BUY</text>
      <text x="150" y="290" text-anchor="middle" class="text">$100</text>
      
      <circle cx="360" cy="230" r="8" fill="#27ae60" />
      <text x="360" y="255" text-anchor="middle" class="text">BUY</text>
      <text x="360" y="270" text-anchor="middle" class="text">$110</text>
      
      <!-- Sell Points -->
      <circle cx="290" cy="200" r="8" fill="#e74c3c" />
      <text x="290" y="185" text-anchor="middle" class="text">SELL</text>
      <text x="290" y="170" text-anchor="middle" class="text">$120</text>
      
      <circle cx="570" cy="160" r="8" fill="#e74c3c" />
      <text x="570" y="145" text-anchor="middle" class="text">SELL</text>
      <text x="570" y="130" text-anchor="middle" class="text">$140</text>
      
      <!-- Profit Arrows -->
      <path d="M 150 240 Q 220 180 290 190" class="arrow" />
      <text x="220" y="200" text-anchor="middle" class="text" fill="#27ae60">+$20 Profit</text>
      
      <path d="M 360 220 Q 465 140 570 150" class="arrow" />
      <text x="465" y="160" text-anchor="middle" class="text" fill="#27ae60">+$30 Profit</text>
      
      <!-- Key Concepts -->
      <rect x="50" y="420" width="200" height="120" class="buy-box" />
      <text x="150" y="445" text-anchor="middle" class="subtitle">BUYING (Going Long)</text>
      <text x="60" y="470" class="text">• Buy when price is low</text>
      <text x="60" y="490" class="text">• Expect price to rise</text>
      <text x="60" y="510" class="text">• Profit = Sell Price - Buy Price</text>
      <text x="60" y="530" class="text">• Example: Buy $100, Sell $120 = $20 profit</text>
      
      <rect x="300" y="420" width="200" height="120" class="sell-box" />
      <text x="400" y="445" text-anchor="middle" class="subtitle">SELLING (Going Short)</text>
      <text x="310" y="470" class="text">• Sell when price is high</text>
      <text x="310" y="490" class="text">• Expect price to fall</text>
      <text x="310" y="510" class="text">• Profit = Sell Price - Buy Price</text>
      <text x="310" y="530" class="text">• Example: Sell $120, Buy $100 = $20 profit</text>
      
      <rect x="550" y="420" width="200" height="120" class="box" />
      <text x="650" y="445" text-anchor="middle" class="subtitle">Key Rules</text>
      <text x="560" y="470" class="text">• Always use stop losses</text>
      <text x="560" y="490" class="text">• Risk only 1-2% per trade</text>
      <text x="560" y="510" class="text">• Plan your trades</text>
      <text x="560" y="530" class="text">• Control your emotions</text>
    </svg>`;
        const filePath = path.join(this.outputDir, 'trading-basics.svg');
        await fs.writeFile(filePath, svgContent);
        return filePath;
    }
    async generateMarketStructureDiagram() {
        const svgContent = `
    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .title { font: bold 24px sans-serif; fill: #2c3e50; }
          .subtitle { font: bold 16px sans-serif; fill: #34495e; }
          .text { font: 12px sans-serif; fill: #2c3e50; }
          .box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
          .central-box { fill: #3498db; stroke: #2c3e50; stroke-width: 2; }
          .participant-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
          .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        </style>
        <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
          <polygon points="0 0, 8 3, 0 6" fill="#e74c3c" />
        </marker>
      </defs>
      
      <!-- Title -->
      <text x="400" y="30" text-anchor="middle" class="title">Financial Market Structure</text>
      
      <!-- Central Market -->
      <rect x="300" y="250" width="200" height="100" class="central-box" rx="10" />
      <text x="400" y="285" text-anchor="middle" class="subtitle" fill="white">FINANCIAL</text>
      <text x="400" y="305" text-anchor="middle" class="subtitle" fill="white">MARKET</text>
      <text x="400" y="325" text-anchor="middle" class="text" fill="white">(Stocks, Forex, Crypto)</text>
      
      <!-- Market Participants -->
      <!-- Retail Traders -->
      <rect x="50" y="80" width="150" height="80" class="participant-box" rx="5" />
      <text x="125" y="105" text-anchor="middle" class="subtitle">Retail Traders</text>
      <text x="60" y="125" class="text">• Individual investors</text>
      <text x="60" y="140" class="text">• Small position sizes</text>
      <text x="60" y="155" class="text">• Use online brokers</text>
      
      <!-- Institutional Investors -->
      <rect x="600" y="80" width="150" height="80" class="participant-box" rx="5" />
      <text x="675" y="105" text-anchor="middle" class="subtitle">Institutions</text>
      <text x="610" y="125" class="text">• Banks, hedge funds</text>
      <text x="610" y="140" class="text">• Large position sizes</text>
      <text x="610" y="155" class="text">• Direct market access</text>
      
      <!-- Market Makers -->
      <rect x="50" y="450" width="150" height="80" class="participant-box" rx="5" />
      <text x="125" y="475" text-anchor="middle" class="subtitle">Market Makers</text>
      <text x="60" y="495" class="text">• Provide liquidity</text>
      <text x="60" y="510" class="text">• Buy/sell continuously</text>
      <text x="60" y="525" class="text">• Profit from spreads</text>
      
      <!-- Central Banks -->
      <rect x="600" y="450" width="150" height="80" class="participant-box" rx="5" />
      <text x="675" y="475" text-anchor="middle" class="subtitle">Central Banks</text>
      <text x="610" y="495" class="text">• Control interest rates</text>
      <text x="610" y="510" class="text">• Monetary policy</text>
      <text x="610" y="525" class="text">• Currency intervention</text>
      
      <!-- Brokers -->
      <rect x="300" y="80" width="200" height="80" class="participant-box" rx="5" />
      <text x="400" y="105" text-anchor="middle" class="subtitle">Brokers</text>
      <text x="310" y="125" class="text">• Facilitate trades between buyers/sellers</text>
      <text x="310" y="140" class="text">• Provide trading platforms</text>
      <text x="310" y="155" class="text">• Charge commissions/spreads</text>
      
      <!-- Arrows showing relationships -->
      <path d="M 125 160 L 350 250" class="arrow" />
      <path d="M 675 160 L 450 250" class="arrow" />
      <path d="M 400 160 L 400 250" class="arrow" />
      <path d="M 125 450 L 350 350" class="arrow" />
      <path d="M 675 450 L 450 350" class="arrow" />
      
      <!-- Market Flow Labels -->
      <text x="200" y="200" class="text" fill="#e74c3c">Orders & Trades</text>
      <text x="550" y="200" class="text" fill="#e74c3c">Orders & Trades</text>
      <text x="200" y="400" class="text" fill="#e74c3c">Liquidity</text>
      <text x="550" y="400" class="text" fill="#e74c3c">Policy Impact</text>
    </svg>`;
        const filePath = path.join(this.outputDir, 'market-structure.svg');
        await fs.writeFile(filePath, svgContent);
        return filePath;
    }
    async generateRiskManagementDiagram() {
        const svgContent = `
    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .title { font: bold 24px sans-serif; fill: #2c3e50; }
          .subtitle { font: bold 16px sans-serif; fill: #34495e; }
          .text { font: 12px sans-serif; fill: #2c3e50; }
          .formula { font: bold 14px monospace; fill: #8e44ad; }
          .good-box { fill: #d5f4e6; stroke: #27ae60; stroke-width: 2; }
          .bad-box { fill: #fadbd8; stroke: #e74c3c; stroke-width: 2; }
          .neutral-box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; }
        </style>
      </defs>
      
      <!-- Title -->
      <text x="400" y="30" text-anchor="middle" class="title">Risk Management: The Foundation of Trading Success</text>
      
      <!-- Position Sizing Section -->
      <rect x="50" y="60" width="700" height="120" class="neutral-box" rx="10" />
      <text x="400" y="85" text-anchor="middle" class="subtitle">Position Sizing Formula</text>
      
      <text x="400" y="110" text-anchor="middle" class="formula">Position Size = (Account Risk ÷ Risk per Share)</text>
      
      <text x="80" y="135" class="text">Example: $10,000 account, 2% risk = $200 risk</text>
      <text x="80" y="150" class="text">Entry: $50, Stop Loss: $48, Risk per share: $2</text>
      <text x="80" y="165" class="text">Position Size: $200 ÷ $2 = 100 shares</text>
      
      <!-- Risk Levels Comparison -->
      <rect x="50" y="200" width="200" height="150" class="good-box" rx="5" />
      <text x="150" y="225" text-anchor="middle" class="subtitle">Conservative (1-2%)</text>
      <text x="60" y="250" class="text">✓ Sustainable long-term</text>
      <text x="60" y="270" class="text">✓ Survives losing streaks</text>
      <text x="60" y="290" class="text">✓ Low stress trading</text>
      <text x="60" y="310" class="text">✓ Compound growth</text>
      <text x="60" y="330" class="text">Example: 10 losses = -20%</text>
      
      <rect x="300" y="200" width="200" height="150" class="neutral-box" rx="5" />
      <text x="400" y="225" text-anchor="middle" class="subtitle">Moderate (3-5%)</text>
      <text x="310" y="250" class="text">⚠ Higher returns possible</text>
      <text x="310" y="270" class="text">⚠ Increased volatility</text>
      <text x="310" y="290" class="text">⚠ Requires discipline</text>
      <text x="310" y="310" class="text">⚠ Faster account swings</text>
      <text x="310" y="330" class="text">Example: 10 losses = -50%</text>
      
      <rect x="550" y="200" width="200" height="150" class="bad-box" rx="5" />
      <text x="650" y="225" text-anchor="middle" class="subtitle">Aggressive (5%+)</text>
      <text x="560" y="250" class="text">✗ High risk of ruin</text>
      <text x="560" y="270" class="text">✗ Emotional trading</text>
      <text x="560" y="290" class="text">✗ Unsustainable</text>
      <text x="560" y="310" class="text">✗ Account blowup risk</text>
      <text x="560" y="330" class="text">Example: 10 losses = -100%</text>
      
      <!-- Stop Loss Types -->
      <rect x="50" y="370" width="700" height="180" class="neutral-box" rx="10" />
      <text x="400" y="395" text-anchor="middle" class="subtitle">Stop Loss Strategies</text>
      
      <!-- Technical Stop -->
      <rect x="70" y="410" width="150" height="80" class="good-box" rx="5" />
      <text x="145" y="430" text-anchor="middle" class="subtitle">Technical Stop</text>
      <text x="80" y="450" class="text">• Below support level</text>
      <text x="80" y="465" class="text">• Above resistance level</text>
      <text x="80" y="480" class="text">• Respects chart structure</text>
      
      <!-- Percentage Stop -->
      <rect x="240" y="410" width="150" height="80" class="neutral-box" rx="5" />
      <text x="315" y="430" text-anchor="middle" class="subtitle">Percentage Stop</text>
      <text x="250" y="450" class="text">• Fixed % from entry</text>
      <text x="250" y="465" class="text">• Easy to calculate</text>
      <text x="250" y="480" class="text">• Ignores market structure</text>
      
      <!-- ATR Stop -->
      <rect x="410" y="410" width="150" height="80" class="good-box" rx="5" />
      <text x="485" y="430" text-anchor="middle" class="subtitle">ATR Stop</text>
      <text x="420" y="450" class="text">• Based on volatility</text>
      <text x="420" y="465" class="text">• Adapts to market</text>
      <text x="420" y="480" class="text">• Dynamic adjustment</text>
      
      <!-- Time Stop -->
      <rect x="580" y="410" width="150" height="80" class="neutral-box" rx="5" />
      <text x="655" y="430" text-anchor="middle" class="subtitle">Time Stop</text>
      <text x="590" y="450" class="text">• Exit after time limit</text>
      <text x="590" y="465" class="text">• Prevents dead money</text>
      <text x="590" y="480" class="text">• Frees up capital</text>
      
      <!-- Key Rule -->
      <text x="400" y="520" text-anchor="middle" class="formula">GOLDEN RULE: Never risk more than you can afford to lose!</text>
      <text x="400" y="540" text-anchor="middle" class="text">Risk management is more important than being right about market direction</text>
    </svg>`;
        const filePath = path.join(this.outputDir, 'risk-management.svg');
        await fs.writeFile(filePath, svgContent);
        return filePath;
    }
    async generateOrderTypesDiagram() {
        // Implementation for order types diagram
        const filePath = path.join(this.outputDir, 'order-types.svg');
        await fs.writeFile(filePath, '<!-- Order Types SVG -->');
        return filePath;
    }
    async generateTradingPsychologyDiagram() {
        // Implementation for trading psychology diagram
        const filePath = path.join(this.outputDir, 'trading-psychology.svg');
        await fs.writeFile(filePath, '<!-- Trading Psychology SVG -->');
        return filePath;
    }
    async generateLearningPathDiagram() {
        // Implementation for learning path diagram
        const filePath = path.join(this.outputDir, 'learning-path.svg');
        await fs.writeFile(filePath, '<!-- Learning Path SVG -->');
        return filePath;
    }
    async generateTradingPlatformDiagram() {
        // Implementation for trading platform diagram
        const filePath = path.join(this.outputDir, 'trading-platform.svg');
        await fs.writeFile(filePath, '<!-- Trading Platform SVG -->');
        return filePath;
    }
    async generateMarketSessionsDiagram() {
        // Implementation for market sessions diagram
        const filePath = path.join(this.outputDir, 'market-sessions.svg');
        await fs.writeFile(filePath, '<!-- Market Sessions SVG -->');
        return filePath;
    }
}
exports.DiagramGenerator = DiagramGenerator;
//# sourceMappingURL=diagram-generator.js.map