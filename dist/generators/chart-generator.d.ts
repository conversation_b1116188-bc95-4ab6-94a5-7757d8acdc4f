export declare class ChartGenerator {
    private outputDir;
    constructor();
    generateTradingCharts(): Promise<{
        [key: string]: string;
    }>;
    private generateCandlestickChart;
    private generateSupportResistanceChart;
    private generateMovingAverageChart;
    private generateRSIChart;
    private generateMACDChart;
    private generateVolumeChart;
    private generateFibonacciChart;
    private generateChartPatternsChart;
    private generateRiskRewardChart;
}
//# sourceMappingURL=chart-generator.d.ts.map