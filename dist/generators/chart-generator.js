"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChartGenerator = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const helpers_1 = require("../utils/helpers");
class ChartGenerator {
    constructor() {
        this.outputDir = path.join('output', 'charts');
    }
    async generateTradingCharts() {
        await (0, helpers_1.ensureDirectoryExists)(this.outputDir);
        const charts = {};
        // Generate various trading charts using HTML5 Canvas and Chart.js
        charts.candlestickChart = await this.generateCandlestickChart();
        charts.supportResistanceChart = await this.generateSupportResistanceChart();
        charts.movingAverageChart = await this.generateMovingAverageChart();
        charts.rsiChart = await this.generateRSIChart();
        charts.macdChart = await this.generateMACDChart();
        charts.volumeChart = await this.generateVolumeChart();
        charts.fibonacciChart = await this.generateFibonacciChart();
        charts.chartPatternsChart = await this.generateChartPatternsChart();
        charts.riskRewardChart = await this.generateRiskRewardChart();
        return charts;
    }
    async generateCandlestickChart() {
        const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>
    </head>
    <body>
        <canvas id="candlestickChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('candlestickChart').getContext('2d');
            
            // Sample candlestick data
            const data = [
                {x: '2024-01-01', o: 1.1850, h: 1.1920, l: 1.1830, c: 1.1890},
                {x: '2024-01-02', o: 1.1890, h: 1.1950, l: 1.1870, c: 1.1920},
                {x: '2024-01-03', o: 1.1920, h: 1.1940, l: 1.1880, c: 1.1900},
                {x: '2024-01-04', o: 1.1900, h: 1.1960, l: 1.1890, c: 1.1940},
                {x: '2024-01-05', o: 1.1940, h: 1.1980, l: 1.1920, c: 1.1970},
                {x: '2024-01-08', o: 1.1970, h: 1.1990, l: 1.1930, c: 1.1950},
                {x: '2024-01-09', o: 1.1950, h: 1.1970, l: 1.1910, c: 1.1930},
                {x: '2024-01-10', o: 1.1930, h: 1.1950, l: 1.1900, c: 1.1920}
            ];

            new Chart(ctx, {
                type: 'candlestick',
                data: {
                    datasets: [{
                        label: 'EUR/USD',
                        data: data,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Candlestick Chart Example - EUR/USD',
                            font: { size: 16 }
                        },
                        legend: {
                            display: true
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            },
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>`;
        const filePath = path.join(this.outputDir, 'candlestick-chart.html');
        await fs.writeFile(filePath, htmlContent);
        return filePath;
    }
    async generateSupportResistanceChart() {
        const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    </head>
    <body>
        <canvas id="supportResistanceChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('supportResistanceChart').getContext('2d');
            
            // Price data
            const priceData = [
                {x: '2024-01-01', y: 1.1850},
                {x: '2024-01-02', y: 1.1920},
                {x: '2024-01-03', y: 1.1900},
                {x: '2024-01-04', y: 1.1940},
                {x: '2024-01-05', y: 1.1970},
                {x: '2024-01-08', y: 1.1950},
                {x: '2024-01-09', y: 1.1930},
                {x: '2024-01-10', y: 1.1920},
                {x: '2024-01-11', y: 1.1960},
                {x: '2024-01-12', y: 1.1980}
            ];

            // Support and Resistance levels
            const supportLevel = 1.1850;
            const resistanceLevel = 1.1980;

            new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'EUR/USD Price',
                        data: priceData,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'Support Level',
                        data: priceData.map(point => ({x: point.x, y: supportLevel})),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        borderDash: [5, 5],
                        pointRadius: 0
                    }, {
                        label: 'Resistance Level',
                        data: priceData.map(point => ({x: point.x, y: resistanceLevel})),
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        borderDash: [5, 5],
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Support and Resistance Levels',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>`;
        const filePath = path.join(this.outputDir, 'support-resistance-chart.html');
        await fs.writeFile(filePath, htmlContent);
        return filePath;
    }
    async generateMovingAverageChart() {
        const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    </head>
    <body>
        <canvas id="movingAverageChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('movingAverageChart').getContext('2d');
            
            // Sample price data with moving averages
            const data = [
                {date: '2024-01-01', price: 1.1850, sma20: 1.1840, sma50: 1.1820},
                {date: '2024-01-02', price: 1.1920, sma20: 1.1850, sma50: 1.1825},
                {date: '2024-01-03', price: 1.1900, sma20: 1.1860, sma50: 1.1830},
                {date: '2024-01-04', price: 1.1940, sma20: 1.1870, sma50: 1.1835},
                {date: '2024-01-05', price: 1.1970, sma20: 1.1885, sma50: 1.1840},
                {date: '2024-01-08', price: 1.1950, sma20: 1.1895, sma50: 1.1845},
                {date: '2024-01-09', price: 1.1930, sma20: 1.1900, sma50: 1.1850},
                {date: '2024-01-10', price: 1.1920, sma20: 1.1905, sma50: 1.1855}
            ];

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(d => d.date),
                    datasets: [{
                        label: 'Price',
                        data: data.map(d => d.price),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'SMA 20',
                        data: data.map(d => d.sma20),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1
                    }, {
                        label: 'SMA 50',
                        data: data.map(d => d.sma50),
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Moving Average Crossover Strategy',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>`;
        const filePath = path.join(this.outputDir, 'moving-average-chart.html');
        await fs.writeFile(filePath, htmlContent);
        return filePath;
    }
    async generateRSIChart() {
        const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
        <canvas id="rsiChart" width="800" height="400"></canvas>
        <script>
            const ctx = document.getElementById('rsiChart').getContext('2d');
            
            // Sample RSI data
            const rsiData = [45, 52, 48, 65, 72, 68, 75, 82, 78, 71, 65, 58, 42, 35, 28, 32, 38, 45, 52, 58];
            const labels = Array.from({length: 20}, (_, i) => \`Day \${i + 1}\`);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'RSI',
                        data: rsiData,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'RSI (Relative Strength Index) - Overbought/Oversold Indicator',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        y: {
                            min: 0,
                            max: 100,
                            title: {
                                display: true,
                                text: 'RSI Value'
                            }
                        }
                    },
                    elements: {
                        point: {
                            backgroundColor: function(context) {
                                const value = context.parsed.y;
                                if (value > 70) return 'red';
                                if (value < 30) return 'green';
                                return 'blue';
                            }
                        }
                    },
                    plugins: {
                        annotation: {
                            annotations: {
                                overbought: {
                                    type: 'line',
                                    yMin: 70,
                                    yMax: 70,
                                    borderColor: 'red',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    label: {
                                        content: 'Overbought (70)',
                                        enabled: true
                                    }
                                },
                                oversold: {
                                    type: 'line',
                                    yMin: 30,
                                    yMax: 30,
                                    borderColor: 'green',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    label: {
                                        content: 'Oversold (30)',
                                        enabled: true
                                    }
                                }
                            }
                        }
                    }
                }
            });
        </script>
    </body>
    </html>`;
        const filePath = path.join(this.outputDir, 'rsi-chart.html');
        await fs.writeFile(filePath, htmlContent);
        return filePath;
    }
    // Add more chart generation methods...
    async generateMACDChart() {
        // Implementation for MACD chart
        const filePath = path.join(this.outputDir, 'macd-chart.html');
        await fs.writeFile(filePath, '<!-- MACD Chart HTML -->');
        return filePath;
    }
    async generateVolumeChart() {
        // Implementation for Volume chart
        const filePath = path.join(this.outputDir, 'volume-chart.html');
        await fs.writeFile(filePath, '<!-- Volume Chart HTML -->');
        return filePath;
    }
    async generateFibonacciChart() {
        // Implementation for Fibonacci chart
        const filePath = path.join(this.outputDir, 'fibonacci-chart.html');
        await fs.writeFile(filePath, '<!-- Fibonacci Chart HTML -->');
        return filePath;
    }
    async generateChartPatternsChart() {
        // Implementation for Chart Patterns
        const filePath = path.join(this.outputDir, 'chart-patterns.html');
        await fs.writeFile(filePath, '<!-- Chart Patterns HTML -->');
        return filePath;
    }
    async generateRiskRewardChart() {
        // Implementation for Risk/Reward chart
        const filePath = path.join(this.outputDir, 'risk-reward-chart.html');
        await fs.writeFile(filePath, '<!-- Risk Reward Chart HTML -->');
        return filePath;
    }
}
exports.ChartGenerator = ChartGenerator;
//# sourceMappingURL=chart-generator.js.map